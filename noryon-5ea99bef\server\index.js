import dotenv from 'dotenv';

// Load environment variables from parent directory FIRST
dotenv.config({ path: '../.env' });

import express from 'express';
import cors from 'cors';
import { InvokeLLM } from '../src/api/llm.js';

const app = express();
const PORT = process.env.PORT || 4000;

app.use(cors());
app.use(express.json({ limit: '2mb' }));

// Health-check
app.get('/health', (_req, res) => res.json({ status: 'ok' }));

// POST /generate/web  { prompt: string }
// Returns a basic diff list now; later we will upgrade to streaming chunks.
app.post('/generate/web', async (req, res) => {
  const { prompt } = req.body || {};
  if (typeof prompt !== 'string' || !prompt.trim()) {
    return res.status(400).json({ error: 'prompt (string) required' });
  }

  try {
    const systemInstruction = `You are a senior React developer generating production-ready Vite+React code only for web. Avoid mobile frameworks.`;

    // Simple invocation for MVP – no streaming yet
    const llmPrompt = `${systemInstruction}\n\nUser request: ${prompt}\n\nReturn ONLY the JSON file tree with real code.`;
    const llmResponse = await InvokeLLM.invoke(llmPrompt, { maxTokens: 4000 });

    res.json({ files: llmResponse });
  } catch (err) {
    console.error('LLM generation failed:', err);
    res.status(500).json({ error: 'code_generation_failed', message: err.message });
  }
});

app.listen(PORT, () => {
  console.log(`Backend running on http://localhost:${PORT}`);
});