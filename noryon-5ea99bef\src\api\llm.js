import { anthropic } from '../lib/claude.js'

export const InvokeLLM = {
  async invoke(prompt, options = {}) {
    try {
      const response = await anthropic.messages.create({
        model: 'claude-sonnet-4-20250514',
        max_tokens: options.maxTokens || options.max_tokens || 4000,
        temperature: options.temperature || 0.7,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      })

      // Return the response text directly to match Base44 API format
      return response.content[0].text
    } catch (error) {
      console.error('LLM invocation error:', error)
      throw error
    }
  },

  async invokeStream(prompt, options = {}) {
    try {
      const stream = await anthropic.messages.create({
        model: 'claude-sonnet-4-20250514',
        max_tokens: options.maxTokens || 4000,
        temperature: options.temperature || 0.7,
        stream: true,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      })

      return stream
    } catch (error) {
      console.error('LLM stream error:', error)
      throw error
    }
  }
}