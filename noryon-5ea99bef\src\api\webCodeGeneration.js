// API client for generating production-ready web code via backend
// Validates response and ensures no placeholders

const API_BASE =
  process.env.NODE_ENV === 'production' ? '/api' : 'http://localhost:4000';

/**
 * Send user prompt to backend `/generate/web` endpoint and return generated file tree.
 * @param {string} prompt - User prompt describing desired web app.
 * @param {AbortSignal} [signal] - Optional AbortSignal for cancellation.
 * @returns {Promise<Object>} JSON mapping file paths to code strings.
 */
export async function generateWebAppCode(prompt, { signal } = {}) {
  if (typeof prompt !== 'string' || prompt.trim().length === 0) {
    throw new Error('Prompt must be a non-empty string');
  }

  const res = await fetch(`${API_BASE}/generate/web`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ prompt }),
    signal
  });

  if (!res.ok) {
    const errText = await res.text().catch(() => '');
    throw new Error(`Backend error ${res.status}: ${errText || res.statusText}`);
  }

  let data;
  try {
    data = await res.json();
  } catch {
    throw new Error('Failed to parse backend response as JSON');
  }

  if (!data || typeof data !== 'object') {
    throw new Error('Invalid backend response structure');
  }

  // Basic placeholder detection – reject obviously incomplete code
  const hasPlaceholder = Object.values(data).some((fileContent) => {
    if (typeof fileContent !== 'string') return true;
    return /TODO|placeholder|Lorem ipsum/i.test(fileContent) || fileContent.length < 40;
  });
  if (hasPlaceholder) {
    throw new Error('Generated code contains placeholders or incomplete content');
  }

  return data;
}