import React, { useState, useRef, useEffect, useCallback } from 'react';
import { 
  <PERSON><PERSON>, 
  Send, 
  Loader2, 
  <PERSON><PERSON>les, 
  Code, 
  FileText, 
  Zap,
  Copy,
  Check,
  User,
  Trash2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { generateWebAppCode } from '@/api/webCodeGeneration';
import { InvokeLLM } from '@/api/llm';

const AIChatAssistant = ({
  fileStructure = {},
  activeFile,
  onCodeGenerated,
  onFileUpdate,
  className = ''
}) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'assistant',
      content: '🚀 **Welcome to the AI Code IDE!**\n\nI can help you build complete websites with just natural language. Try saying:\n\n💡 **"Create a modern portfolio website with dark theme"**\n💡 **"Build a restaurant website with menu and contact form"**\n💡 **"Make a landing page for a tech startup"**\n💡 **"Generate a blog website with responsive design"**\n\nI\'ll create all the files (HTML, CSS, JavaScript) and you\'ll see the live preview instantly!\n\n✨ **Other things I can do:**\n• Fix bugs and errors in your code\n• Add new features to existing websites\n• Explain how code works\n• Optimize performance\n\n**What would you like to build today?**',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isThinking, setIsThinking] = useState(false);
  const [copiedMessageId, setCopiedMessageId] = useState(null);
  const messagesEndRef = useRef(null);
  const textareaRef = useRef(null);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle message send
  const handleSendMessage = useCallback(async () => {
    if (!inputMessage.trim() || isThinking) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputMessage.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsThinking(true);

    try {
      // Determine the type of request
      const message = userMessage.content.toLowerCase();
      let response = '';
      let generatedFiles = null;

      if (message.includes('create') || message.includes('build') || message.includes('generate') || message.includes('make')) {
        // Website generation request
        const thinkingMessage = {
          id: Date.now() + 1,
          type: 'assistant',
          content: '🤖 Generating your website... This may take a moment.',
          timestamp: new Date(),
          isThinking: true
        };
        setMessages(prev => [...prev, thinkingMessage]);

        try {
          const result = await generateWebAppCode(userMessage.content);
          
          if (result && result.files) {
            let parsedFiles = {};
            
            // Parse the generated files
            if (typeof result.files === 'string') {
              const jsonMatch = result.files.match(/```json\n([\s\S]*?)\n```/);
              if (jsonMatch) {
                parsedFiles = JSON.parse(jsonMatch[1]);
              } else {
                parsedFiles = JSON.parse(result.files);
              }
            } else {
              parsedFiles = result.files;
            }

            generatedFiles = parsedFiles;
            response = `✅ **Website Generated Successfully!**\n\nI've created a complete website based on your request. The generated files include:\n\n${Object.keys(parsedFiles).map(file => `• ${file}`).join('\n')}\n\nThe files have been loaded into your editor. You can now:\n• Edit the code in the editor\n• See the live preview on the right\n• Ask me to modify specific parts\n• Add new features or pages`;
          } else {
            response = '❌ Sorry, I encountered an issue generating your website. Please try rephrasing your request or provide more specific details about what you want to build.';
          }
        } catch (error) {
          console.error('Website generation error:', error);
          response = `❌ **Generation Failed**\n\nI encountered an error while generating your website: ${error.message}\n\nPlease try:\n• Being more specific about your requirements\n• Simplifying your request\n• Checking your internet connection`;
        }

        // Remove thinking message and add response
        setMessages(prev => prev.filter(msg => !msg.isThinking));
      } else if (message.includes('fix') || message.includes('debug') || message.includes('error')) {
        // Code fixing request
        const currentCode = activeFile ? fileStructure[activeFile] : '';
        const prompt = `Help fix this code issue: ${userMessage.content}\n\nCurrent file: ${activeFile || 'No file selected'}\nCode:\n${currentCode.slice(0, 1000)}`;
        
        response = await InvokeLLM.invoke(prompt, { maxTokens: 1000 });
      } else if (message.includes('explain') || message.includes('what does') || message.includes('how does')) {
        // Code explanation request
        const currentCode = activeFile ? fileStructure[activeFile] : '';
        const prompt = `Explain this code: ${userMessage.content}\n\nFile: ${activeFile || 'No file selected'}\nCode:\n${currentCode.slice(0, 1000)}`;
        
        response = await InvokeLLM.invoke(prompt, { maxTokens: 800 });
      } else {
        // General coding assistance
        const context = activeFile ? `Current file: ${activeFile}\nCode context:\n${fileStructure[activeFile]?.slice(0, 500)}` : 'No file currently open';
        const prompt = `${userMessage.content}\n\nContext:\n${context}`;
        
        response = await InvokeLLM.invoke(prompt, { maxTokens: 1000 });
      }

      const assistantMessage = {
        id: Date.now() + 2,
        type: 'assistant',
        content: response,
        timestamp: new Date(),
        generatedFiles
      };

      setMessages(prev => [...prev, assistantMessage]);

      // If files were generated, update the editor
      if (generatedFiles) {
        onCodeGenerated?.(generatedFiles);
      }

    } catch (error) {
      console.error('AI assistant error:', error);
      const errorMessage = {
        id: Date.now() + 3,
        type: 'assistant',
        content: `❌ **Error**: I encountered an issue processing your request: ${error.message}\n\nPlease try again or rephrase your question.`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsThinking(false);
    }
  }, [inputMessage, isThinking, activeFile, fileStructure, onCodeGenerated]);

  // Handle key press
  const handleKeyPress = useCallback((e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  // Copy message content
  const copyMessage = useCallback(async (messageId, content) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopiedMessageId(messageId);
      setTimeout(() => setCopiedMessageId(null), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  }, []);

  // Clear chat
  const clearChat = useCallback(() => {
    setMessages([
      {
        id: 1,
        type: 'assistant',
        content: '👋 Chat cleared! How can I help you with your code today?',
        timestamp: new Date()
      }
    ]);
  }, []);

  // Quick action buttons
  const quickActions = [
    {
      label: 'Generate Website',
      icon: <Sparkles className="w-3 h-3" />,
      prompt: 'Create a modern responsive website with'
    },
    {
      label: 'Fix Code',
      icon: <Zap className="w-3 h-3" />,
      prompt: 'Fix the issue in my code:'
    },
    {
      label: 'Add Feature',
      icon: <Code className="w-3 h-3" />,
      prompt: 'Add a new feature to my website:'
    },
    {
      label: 'Explain Code',
      icon: <FileText className="w-3 h-3" />,
      prompt: 'Explain how this code works:'
    }
  ];

  return (
    <div className={`flex flex-col h-full bg-gray-900 text-white ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-3 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <Bot className="w-4 h-4 text-blue-400" />
          <span className="text-sm font-medium">AI Assistant</span>
          <Badge variant="outline" className="text-xs">
            Online
          </Badge>
        </div>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={clearChat}
          className="h-7 px-2"
          title="Clear Chat"
        >
          <Trash2 className="w-3 h-3" />
        </Button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            {message.type === 'assistant' && (
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                <Bot className="w-4 h-4" />
              </div>
            )}
            
            <div className={`max-w-[80%] ${message.type === 'user' ? 'order-first' : ''}`}>
              <div
                className={`p-3 rounded-lg ${
                  message.type === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-800 text-gray-100'
                }`}
              >
                <div className="whitespace-pre-wrap text-sm">{message.content}</div>
                
                {message.generatedFiles && (
                  <div className="mt-2 p-2 bg-gray-700 rounded text-xs">
                    <div className="flex items-center gap-1 mb-1">
                      <Code className="w-3 h-3" />
                      <span>Generated Files:</span>
                    </div>
                    <div className="text-gray-300">
                      {Object.keys(message.generatedFiles).slice(0, 5).map(file => (
                        <div key={file}>• {file}</div>
                      ))}
                      {Object.keys(message.generatedFiles).length > 5 && (
                        <div>... and {Object.keys(message.generatedFiles).length - 5} more</div>
                      )}
                    </div>
                  </div>
                )}
              </div>
              
              <div className="flex items-center justify-between mt-1">
                <span className="text-xs text-gray-500">
                  {message.timestamp.toLocaleTimeString()}
                </span>
                
                {message.type === 'assistant' && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => copyMessage(message.id, message.content)}
                    className="h-6 px-2 text-gray-500 hover:text-white"
                  >
                    {copiedMessageId === message.id ? (
                      <Check className="w-3 h-3" />
                    ) : (
                      <Copy className="w-3 h-3" />
                    )}
                  </Button>
                )}
              </div>
            </div>
            
            {message.type === 'user' && (
              <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                <User className="w-4 h-4" />
              </div>
            )}
          </div>
        ))}
        
        {isThinking && (
          <div className="flex gap-3 justify-start">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
              <Bot className="w-4 h-4" />
            </div>
            <div className="bg-gray-800 p-3 rounded-lg">
              <div className="flex items-center gap-2 text-sm text-gray-300">
                <Loader2 className="w-4 h-4 animate-spin" />
                Thinking...
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Quick Actions */}
      <div className="p-3 border-t border-gray-700">
        <div className="flex flex-wrap gap-2 mb-3">
          {quickActions.map((action) => (
            <Button
              key={action.label}
              size="sm"
              variant="outline"
              onClick={() => setInputMessage(action.prompt + ' ')}
              className="h-7 text-xs"
            >
              {action.icon}
              {action.label}
            </Button>
          ))}
        </div>

        {/* Input */}
        <div className="flex gap-2">
          <Textarea
            ref={textareaRef}
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Ask me to generate a website, fix code, or explain something..."
            className="flex-1 bg-gray-800 border-gray-600 text-white placeholder-gray-400 text-sm resize-none"
            rows={2}
            disabled={isThinking}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isThinking}
            className="px-3"
          >
            {isThinking ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AIChatAssistant;
