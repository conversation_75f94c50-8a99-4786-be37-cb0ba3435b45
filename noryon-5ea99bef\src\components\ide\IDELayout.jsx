import React, { useState, useCallback, useEffect } from 'react';
import { 
  Split, 
  Code, 
  Eye, 
  Terminal as TerminalI<PERSON>, 
  Bot,
  Settings,
  Play,
  Save,
  Folder
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import FileSystemManager from './FileSystemManager';
import CodeEditor from './CodeEditor';
import FileTabs from './FileTabs';
import WebTerminal from './WebTerminal';
import LivePreview from './LivePreview';
import AIChatAssistant from './AIChatAssistant';
import CodeAnalyzer from './CodeAnalyzer';
import IDETestSuite from './IDETestSuite';

const IDELayout = ({
  initialFiles = {},
  onFilesChange,
  onAIAssist,
  onRunCode,
  className = ''
}) => {
  const [fileStructure, setFileStructure] = useState(initialFiles);
  const [openFiles, setOpenFiles] = useState([]);
  const [activeFile, setActiveFile] = useState(null);
  const [unsavedFiles, setUnsavedFiles] = useState(new Set());
  const [layout, setLayout] = useState('split'); // 'split', 'editor', 'preview'
  const [sidebarWidth, setSidebarWidth] = useState(250);
  const [bottomPanelHeight, setBottomPanelHeight] = useState(200);
  const [showBottomPanel, setShowBottomPanel] = useState(true);
  const [activeBottomTab, setActiveBottomTab] = useState('terminal');

  // Initialize with first file if available
  useEffect(() => {
    const fileKeys = Object.keys(initialFiles);
    if (fileKeys.length > 0 && openFiles.length === 0) {
      const firstFile = fileKeys[0];
      setOpenFiles([firstFile]);
      setActiveFile(firstFile);
    }
  }, [initialFiles, openFiles.length]);

  // Handle file selection from file explorer
  const handleFileSelect = useCallback((filePath) => {
    if (!openFiles.includes(filePath)) {
      setOpenFiles(prev => [...prev, filePath]);
    }
    setActiveFile(filePath);
  }, [openFiles]);

  // Handle file close
  const handleFileClose = useCallback((filePath) => {
    setOpenFiles(prev => {
      const newOpenFiles = prev.filter(f => f !== filePath);
      
      // If closing active file, switch to another open file
      if (filePath === activeFile) {
        const currentIndex = prev.indexOf(filePath);
        const nextFile = newOpenFiles[currentIndex] || newOpenFiles[currentIndex - 1] || null;
        setActiveFile(nextFile);
      }
      
      return newOpenFiles;
    });
    
    // Remove from unsaved files
    setUnsavedFiles(prev => {
      const newSet = new Set(prev);
      newSet.delete(filePath);
      return newSet;
    });
  }, [activeFile]);

  // Handle close all files
  const handleFileCloseAll = useCallback(() => {
    setOpenFiles([]);
    setActiveFile(null);
    setUnsavedFiles(new Set());
  }, []);

  // Handle close other files
  const handleFileCloseOthers = useCallback((keepFile) => {
    setOpenFiles([keepFile]);
    setActiveFile(keepFile);
    setUnsavedFiles(prev => {
      const newSet = new Set();
      if (prev.has(keepFile)) {
        newSet.add(keepFile);
      }
      return newSet;
    });
  }, []);

  // Handle file content change
  const handleFileChange = useCallback((newContent) => {
    if (activeFile) {
      setFileStructure(prev => ({
        ...prev,
        [activeFile]: newContent
      }));
      
      setUnsavedFiles(prev => new Set([...prev, activeFile]));
      onFilesChange?.({ ...fileStructure, [activeFile]: newContent });
    }
  }, [activeFile, fileStructure, onFilesChange]);

  // Handle file save
  const handleFileSave = useCallback((content, fileName) => {
    if (fileName) {
      setFileStructure(prev => ({
        ...prev,
        [fileName]: content
      }));
      
      setUnsavedFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(fileName);
        return newSet;
      });
      
      onFilesChange?.({ ...fileStructure, [fileName]: content });
    }
  }, [fileStructure, onFilesChange]);

  // Handle file creation
  const handleFileCreate = useCallback((filePath, content = '') => {
    setFileStructure(prev => ({
      ...prev,
      [filePath]: content
    }));
    
    // Open the new file
    if (!openFiles.includes(filePath)) {
      setOpenFiles(prev => [...prev, filePath]);
    }
    setActiveFile(filePath);
  }, [openFiles]);

  // Handle file deletion
  const handleFileDelete = useCallback((filePath) => {
    setFileStructure(prev => {
      const newStructure = { ...prev };
      delete newStructure[filePath];
      return newStructure;
    });
    
    // Close file if open
    handleFileClose(filePath);
  }, [handleFileClose]);

  // Handle file rename
  const handleFileRename = useCallback((oldPath, newPath) => {
    setFileStructure(prev => {
      const newStructure = { ...prev };
      newStructure[newPath] = newStructure[oldPath];
      delete newStructure[oldPath];
      return newStructure;
    });
    
    // Update open files
    setOpenFiles(prev => prev.map(f => f === oldPath ? newPath : f));
    
    // Update active file
    if (activeFile === oldPath) {
      setActiveFile(newPath);
    }
    
    // Update unsaved files
    setUnsavedFiles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(oldPath)) {
        newSet.delete(oldPath);
        newSet.add(newPath);
      }
      return newSet;
    });
  }, [activeFile]);

  // Handle folder creation
  const handleFolderCreate = useCallback((folderPath) => {
    // Create a placeholder file to represent the folder
    const placeholderFile = `${folderPath}/.gitkeep`;
    setFileStructure(prev => ({
      ...prev,
      [placeholderFile]: ''
    }));
  }, []);

  // Handle file structure update from AI
  const handleFileStructureUpdate = useCallback((newFiles) => {
    setFileStructure(newFiles);

    // Open the main file if available
    const mainFiles = ['index.html', 'src/App.jsx', 'src/main.jsx', 'App.js'];
    const fileToOpen = mainFiles.find(file => newFiles[file]) || Object.keys(newFiles)[0];

    if (fileToOpen) {
      if (!openFiles.includes(fileToOpen)) {
        setOpenFiles(prev => [fileToOpen, ...prev]);
      }
      setActiveFile(fileToOpen);
    }

    onFilesChange?.(newFiles);
  }, [openFiles, onFilesChange]);

  // Handle AI assist
  const handleAIAssist = useCallback((context) => {
    onAIAssist?.({
      ...context,
      fileStructure,
      openFiles,
      activeFile
    });
  }, [onAIAssist, fileStructure, openFiles, activeFile]);

  // Handle run code
  const handleRunCode = useCallback((code, fileName) => {
    onRunCode?.(code, fileName, fileStructure);
  }, [onRunCode, fileStructure]);

  // Handle terminal command with file system integration
  const handleTerminalCommand = useCallback(async (command, directory) => {
    const args = command.split(' ');
    const cmd = args[0].toLowerCase();

    try {
      switch (cmd) {
        case 'ls':
          // List current project files
          const files = Object.keys(fileStructure);
          if (files.length === 0) {
            return 'No files in project';
          }

          const directories = new Set();
          const fileList = [];

          files.forEach(file => {
            const parts = file.split('/');
            if (parts.length > 1) {
              directories.add(parts[0] + '/');
            } else {
              fileList.push(file);
            }
          });

          let result = '';
          if (directories.size > 0) {
            result += '📁 Directories:\n';
            Array.from(directories).forEach(dir => {
              result += `  ${dir}\n`;
            });
            result += '\n';
          }

          if (fileList.length > 0) {
            result += '📄 Files:\n';
            fileList.forEach(file => {
              result += `  ${file}\n`;
            });
          }

          return result;

        case 'cat':
          const fileName = args[1];
          if (!fileName) {
            return 'Usage: cat <filename>';
          }

          if (fileStructure[fileName]) {
            return `Contents of ${fileName}:\n\n${fileStructure[fileName]}`;
          } else {
            return `File not found: ${fileName}`;
          }

        case 'touch':
          const newFileName = args[1];
          if (!newFileName) {
            return 'Usage: touch <filename>';
          }

          handleFileCreate(newFileName, '');
          return `Created file: ${newFileName}`;

        case 'rm':
          const fileToDelete = args[1];
          if (!fileToDelete) {
            return 'Usage: rm <filename>';
          }

          if (fileStructure[fileToDelete]) {
            handleFileDelete(fileToDelete);
            return `Deleted file: ${fileToDelete}`;
          } else {
            return `File not found: ${fileToDelete}`;
          }

        case 'wc':
          const wcFileName = args[1];
          if (!wcFileName) {
            return 'Usage: wc <filename>';
          }

          if (fileStructure[wcFileName]) {
            const content = fileStructure[wcFileName];
            const lines = content.split('\n').length;
            const words = content.split(/\s+/).filter(w => w.length > 0).length;
            const chars = content.length;
            return `${lines} lines, ${words} words, ${chars} characters in ${wcFileName}`;
          } else {
            return `File not found: ${wcFileName}`;
          }

        default:
          // Fallback to simulated execution
          return new Promise((resolve) => {
            setTimeout(() => {
              resolve(`Command executed: ${command}`);
            }, 1000);
          });
      }
    } catch (error) {
      return `Error: ${error.message}`;
    }
  }, [fileStructure, handleFileCreate, handleFileDelete]);

  const currentFileContent = activeFile ? fileStructure[activeFile] || '' : '';

  return (
    <div className={`flex h-full bg-gray-900 text-white ${className}`}>
      {/* Sidebar - File Explorer */}
      <div 
        className="border-r border-gray-700 flex-shrink-0"
        style={{ width: sidebarWidth }}
      >
        <FileSystemManager
          fileStructure={fileStructure}
          onFileSelect={handleFileSelect}
          onFileCreate={handleFileCreate}
          onFileDelete={handleFileDelete}
          onFileRename={handleFileRename}
          onFolderCreate={handleFolderCreate}
          activeFile={activeFile}
          className="h-full"
        />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* File Tabs */}
        <FileTabs
          openFiles={openFiles}
          activeFile={activeFile}
          onFileSelect={setActiveFile}
          onFileClose={handleFileClose}
          onFileCloseAll={handleFileCloseAll}
          onFileCloseOthers={handleFileCloseOthers}
          unsavedFiles={unsavedFiles}
        />

        {/* Editor/Preview Area */}
        <div className="flex-1 flex">
          {layout === 'split' && (
            <>
              {/* Code Editor */}
              <div className="flex-1 border-r border-gray-700">
                {activeFile ? (
                  <CodeEditor
                    value={currentFileContent}
                    onChange={handleFileChange}
                    fileName={activeFile}
                    onSave={handleFileSave}
                    onRun={handleRunCode}
                    onAIAssist={handleAIAssist}
                    className="h-full"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="text-center">
                      <Code className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p>No file selected</p>
                      <p className="text-sm">Open a file from the explorer to start coding</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Live Preview */}
              <div className="flex-1">
                <LivePreview
                  fileStructure={fileStructure}
                  activeFile={activeFile}
                  className="h-full"
                />
              </div>
            </>
          )}

          {layout === 'editor' && (
            <div className="flex-1">
              {activeFile ? (
                <CodeEditor
                  value={currentFileContent}
                  onChange={handleFileChange}
                  fileName={activeFile}
                  onSave={handleFileSave}
                  onRun={handleRunCode}
                  onAIAssist={handleAIAssist}
                  className="h-full"
                />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Code className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p>No file selected</p>
                    <p className="text-sm">Open a file from the explorer to start coding</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {layout === 'preview' && (
            <div className="flex-1">
              <LivePreview
                fileStructure={fileStructure}
                activeFile={activeFile}
                className="h-full"
              />
            </div>
          )}
        </div>

        {/* Bottom Panel */}
        {showBottomPanel && (
          <div 
            className="border-t border-gray-700 flex-shrink-0"
            style={{ height: bottomPanelHeight }}
          >
            <Tabs value={activeBottomTab} onValueChange={setActiveBottomTab} className="h-full flex flex-col">
              <TabsList className="bg-gray-800 border-b border-gray-700 rounded-none h-10">
                <TabsTrigger value="terminal" className="flex items-center gap-2">
                  <TerminalIcon className="w-4 h-4" />
                  Terminal
                </TabsTrigger>
                <TabsTrigger value="ai" className="flex items-center gap-2">
                  <Bot className="w-4 h-4" />
                  AI Assistant
                </TabsTrigger>
                <TabsTrigger value="analyzer" className="flex items-center gap-2">
                  <Code className="w-4 h-4" />
                  Code Analysis
                </TabsTrigger>
                <TabsTrigger value="output" className="flex items-center gap-2">
                  <Play className="w-4 h-4" />
                  Output
                </TabsTrigger>
                <TabsTrigger value="tests" className="flex items-center gap-2">
                  <Zap className="w-4 h-4" />
                  Tests
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="terminal" className="flex-1 m-0">
                <WebTerminal
                  onCommand={handleTerminalCommand}
                  className="h-full"
                  title="Integrated Terminal"
                />
              </TabsContent>
              
              <TabsContent value="ai" className="flex-1 m-0">
                <AIChatAssistant
                  fileStructure={fileStructure}
                  activeFile={activeFile}
                  onCodeGenerated={handleFileStructureUpdate}
                  onFileUpdate={handleFileChange}
                  className="h-full"
                />
              </TabsContent>

              <TabsContent value="analyzer" className="flex-1 m-0">
                <CodeAnalyzer
                  fileStructure={fileStructure}
                  activeFile={activeFile}
                  className="h-full"
                />
              </TabsContent>
              
              <TabsContent value="output" className="flex-1 m-0 p-4">
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Play className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p>Output Console</p>
                    <p className="text-sm">Code execution results will appear here</p>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="tests" className="flex-1 m-0">
                <IDETestSuite
                  className="h-full"
                />
              </TabsContent>
            </Tabs>
          </div>
        )}
      </div>

      {/* Layout Controls */}
      <div className="absolute top-4 right-4 flex items-center gap-2 bg-gray-800 rounded-lg p-2 border border-gray-700">
        <Button
          size="sm"
          variant={layout === 'editor' ? 'default' : 'ghost'}
          onClick={() => setLayout('editor')}
          className="h-8"
        >
          <Code className="w-4 h-4" />
        </Button>
        <Button
          size="sm"
          variant={layout === 'split' ? 'default' : 'ghost'}
          onClick={() => setLayout('split')}
          className="h-8"
        >
          <Split className="w-4 h-4" />
        </Button>
        <Button
          size="sm"
          variant={layout === 'preview' ? 'default' : 'ghost'}
          onClick={() => setLayout('preview')}
          className="h-8"
        >
          <Eye className="w-4 h-4" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          onClick={() => setShowBottomPanel(!showBottomPanel)}
          className="h-8"
        >
          <TerminalIcon className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

export default IDELayout;
