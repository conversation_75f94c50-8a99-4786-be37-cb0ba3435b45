import React, { useState, useCallback, useEffect } from 'react';
import { 
  Split, 
  Code, 
  Eye, 
  Terminal as TerminalIcon, 
  Bot,
  Settings,
  Play,
  Save,
  Folder
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import FileSystemManager from './FileSystemManager';
import CodeEditor from './CodeEditor';
import FileTabs from './FileTabs';
import WebTerminal from './WebTerminal';

const IDELayout = ({
  initialFiles = {},
  onFilesChange,
  onAIAssist,
  onRunCode,
  className = ''
}) => {
  const [fileStructure, setFileStructure] = useState(initialFiles);
  const [openFiles, setOpenFiles] = useState([]);
  const [activeFile, setActiveFile] = useState(null);
  const [unsavedFiles, setUnsavedFiles] = useState(new Set());
  const [layout, setLayout] = useState('split'); // 'split', 'editor', 'preview'
  const [sidebarWidth, setSidebarWidth] = useState(250);
  const [bottomPanelHeight, setBottomPanelHeight] = useState(200);
  const [showBottomPanel, setShowBottomPanel] = useState(true);
  const [activeBottomTab, setActiveBottomTab] = useState('terminal');

  // Initialize with first file if available
  useEffect(() => {
    const fileKeys = Object.keys(initialFiles);
    if (fileKeys.length > 0 && openFiles.length === 0) {
      const firstFile = fileKeys[0];
      setOpenFiles([firstFile]);
      setActiveFile(firstFile);
    }
  }, [initialFiles, openFiles.length]);

  // Handle file selection from file explorer
  const handleFileSelect = useCallback((filePath) => {
    if (!openFiles.includes(filePath)) {
      setOpenFiles(prev => [...prev, filePath]);
    }
    setActiveFile(filePath);
  }, [openFiles]);

  // Handle file close
  const handleFileClose = useCallback((filePath) => {
    setOpenFiles(prev => {
      const newOpenFiles = prev.filter(f => f !== filePath);
      
      // If closing active file, switch to another open file
      if (filePath === activeFile) {
        const currentIndex = prev.indexOf(filePath);
        const nextFile = newOpenFiles[currentIndex] || newOpenFiles[currentIndex - 1] || null;
        setActiveFile(nextFile);
      }
      
      return newOpenFiles;
    });
    
    // Remove from unsaved files
    setUnsavedFiles(prev => {
      const newSet = new Set(prev);
      newSet.delete(filePath);
      return newSet;
    });
  }, [activeFile]);

  // Handle close all files
  const handleFileCloseAll = useCallback(() => {
    setOpenFiles([]);
    setActiveFile(null);
    setUnsavedFiles(new Set());
  }, []);

  // Handle close other files
  const handleFileCloseOthers = useCallback((keepFile) => {
    setOpenFiles([keepFile]);
    setActiveFile(keepFile);
    setUnsavedFiles(prev => {
      const newSet = new Set();
      if (prev.has(keepFile)) {
        newSet.add(keepFile);
      }
      return newSet;
    });
  }, []);

  // Handle file content change
  const handleFileChange = useCallback((newContent) => {
    if (activeFile) {
      setFileStructure(prev => ({
        ...prev,
        [activeFile]: newContent
      }));
      
      setUnsavedFiles(prev => new Set([...prev, activeFile]));
      onFilesChange?.({ ...fileStructure, [activeFile]: newContent });
    }
  }, [activeFile, fileStructure, onFilesChange]);

  // Handle file save
  const handleFileSave = useCallback((content, fileName) => {
    if (fileName) {
      setFileStructure(prev => ({
        ...prev,
        [fileName]: content
      }));
      
      setUnsavedFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(fileName);
        return newSet;
      });
      
      onFilesChange?.({ ...fileStructure, [fileName]: content });
    }
  }, [fileStructure, onFilesChange]);

  // Handle file creation
  const handleFileCreate = useCallback((filePath, content = '') => {
    setFileStructure(prev => ({
      ...prev,
      [filePath]: content
    }));
    
    // Open the new file
    if (!openFiles.includes(filePath)) {
      setOpenFiles(prev => [...prev, filePath]);
    }
    setActiveFile(filePath);
  }, [openFiles]);

  // Handle file deletion
  const handleFileDelete = useCallback((filePath) => {
    setFileStructure(prev => {
      const newStructure = { ...prev };
      delete newStructure[filePath];
      return newStructure;
    });
    
    // Close file if open
    handleFileClose(filePath);
  }, [handleFileClose]);

  // Handle file rename
  const handleFileRename = useCallback((oldPath, newPath) => {
    setFileStructure(prev => {
      const newStructure = { ...prev };
      newStructure[newPath] = newStructure[oldPath];
      delete newStructure[oldPath];
      return newStructure;
    });
    
    // Update open files
    setOpenFiles(prev => prev.map(f => f === oldPath ? newPath : f));
    
    // Update active file
    if (activeFile === oldPath) {
      setActiveFile(newPath);
    }
    
    // Update unsaved files
    setUnsavedFiles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(oldPath)) {
        newSet.delete(oldPath);
        newSet.add(newPath);
      }
      return newSet;
    });
  }, [activeFile]);

  // Handle folder creation
  const handleFolderCreate = useCallback((folderPath) => {
    // Create a placeholder file to represent the folder
    const placeholderFile = `${folderPath}/.gitkeep`;
    setFileStructure(prev => ({
      ...prev,
      [placeholderFile]: ''
    }));
  }, []);

  // Handle AI assist
  const handleAIAssist = useCallback((context) => {
    onAIAssist?.({
      ...context,
      fileStructure,
      openFiles,
      activeFile
    });
  }, [onAIAssist, fileStructure, openFiles, activeFile]);

  // Handle run code
  const handleRunCode = useCallback((code, fileName) => {
    onRunCode?.(code, fileName, fileStructure);
  }, [onRunCode, fileStructure]);

  // Handle terminal command
  const handleTerminalCommand = useCallback(async (command, directory) => {
    // Simulate command execution
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(`Command executed: ${command}`);
      }, 1000);
    });
  }, []);

  const currentFileContent = activeFile ? fileStructure[activeFile] || '' : '';

  return (
    <div className={`flex h-full bg-gray-900 text-white ${className}`}>
      {/* Sidebar - File Explorer */}
      <div 
        className="border-r border-gray-700 flex-shrink-0"
        style={{ width: sidebarWidth }}
      >
        <FileSystemManager
          fileStructure={fileStructure}
          onFileSelect={handleFileSelect}
          onFileCreate={handleFileCreate}
          onFileDelete={handleFileDelete}
          onFileRename={handleFileRename}
          onFolderCreate={handleFolderCreate}
          activeFile={activeFile}
          className="h-full"
        />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* File Tabs */}
        <FileTabs
          openFiles={openFiles}
          activeFile={activeFile}
          onFileSelect={setActiveFile}
          onFileClose={handleFileClose}
          onFileCloseAll={handleFileCloseAll}
          onFileCloseOthers={handleFileCloseOthers}
          unsavedFiles={unsavedFiles}
        />

        {/* Editor/Preview Area */}
        <div className="flex-1 flex">
          {layout === 'split' && (
            <>
              {/* Code Editor */}
              <div className="flex-1 border-r border-gray-700">
                {activeFile ? (
                  <CodeEditor
                    value={currentFileContent}
                    onChange={handleFileChange}
                    fileName={activeFile}
                    onSave={handleFileSave}
                    onRun={handleRunCode}
                    onAIAssist={handleAIAssist}
                    className="h-full"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="text-center">
                      <Code className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p>No file selected</p>
                      <p className="text-sm">Open a file from the explorer to start coding</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Live Preview */}
              <div className="flex-1">
                <div className="h-full bg-white">
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="text-center">
                      <Eye className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p>Live Preview</p>
                      <p className="text-sm">Your app preview will appear here</p>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {layout === 'editor' && (
            <div className="flex-1">
              {activeFile ? (
                <CodeEditor
                  value={currentFileContent}
                  onChange={handleFileChange}
                  fileName={activeFile}
                  onSave={handleFileSave}
                  onRun={handleRunCode}
                  onAIAssist={handleAIAssist}
                  className="h-full"
                />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Code className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p>No file selected</p>
                    <p className="text-sm">Open a file from the explorer to start coding</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {layout === 'preview' && (
            <div className="flex-1 bg-white">
              <div className="flex items-center justify-center h-full text-gray-500">
                <div className="text-center">
                  <Eye className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p>Live Preview</p>
                  <p className="text-sm">Your app preview will appear here</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Bottom Panel */}
        {showBottomPanel && (
          <div 
            className="border-t border-gray-700 flex-shrink-0"
            style={{ height: bottomPanelHeight }}
          >
            <Tabs value={activeBottomTab} onValueChange={setActiveBottomTab} className="h-full flex flex-col">
              <TabsList className="bg-gray-800 border-b border-gray-700 rounded-none h-10">
                <TabsTrigger value="terminal" className="flex items-center gap-2">
                  <TerminalIcon className="w-4 h-4" />
                  Terminal
                </TabsTrigger>
                <TabsTrigger value="ai" className="flex items-center gap-2">
                  <Bot className="w-4 h-4" />
                  AI Assistant
                </TabsTrigger>
                <TabsTrigger value="output" className="flex items-center gap-2">
                  <Play className="w-4 h-4" />
                  Output
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="terminal" className="flex-1 m-0">
                <WebTerminal
                  onCommand={handleTerminalCommand}
                  className="h-full"
                  title="Integrated Terminal"
                />
              </TabsContent>
              
              <TabsContent value="ai" className="flex-1 m-0 p-4">
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Bot className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p>AI Assistant</p>
                    <p className="text-sm">Get AI-powered coding help and suggestions</p>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="output" className="flex-1 m-0 p-4">
                <div className="flex items-center justify-center h-full text-gray-500">
                  <div className="text-center">
                    <Play className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p>Output Console</p>
                    <p className="text-sm">Code execution results will appear here</p>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </div>

      {/* Layout Controls */}
      <div className="absolute top-4 right-4 flex items-center gap-2 bg-gray-800 rounded-lg p-2 border border-gray-700">
        <Button
          size="sm"
          variant={layout === 'editor' ? 'default' : 'ghost'}
          onClick={() => setLayout('editor')}
          className="h-8"
        >
          <Code className="w-4 h-4" />
        </Button>
        <Button
          size="sm"
          variant={layout === 'split' ? 'default' : 'ghost'}
          onClick={() => setLayout('split')}
          className="h-8"
        >
          <Split className="w-4 h-4" />
        </Button>
        <Button
          size="sm"
          variant={layout === 'preview' ? 'default' : 'ghost'}
          onClick={() => setLayout('preview')}
          className="h-8"
        >
          <Eye className="w-4 h-4" />
        </Button>
        <Button
          size="sm"
          variant="ghost"
          onClick={() => setShowBottomPanel(!showBottomPanel)}
          className="h-8"
        >
          <TerminalIcon className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};

export default IDELayout;
