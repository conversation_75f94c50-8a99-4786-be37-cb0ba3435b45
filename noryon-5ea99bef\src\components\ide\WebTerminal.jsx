import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebLinksAddon } from 'xterm-addon-web-links';
import { 
  Terminal as TerminalIcon, 
  X, 
  Maximize2, 
  Minimize2, 
  RotateCcw,
  Settings,
  Plus
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import 'xterm/css/xterm.css';

const WebTerminal = ({
  onCommand,
  className = '',
  initialDirectory = '/',
  title = 'Terminal'
}) => {
  const terminalRef = useRef(null);
  const terminalInstance = useRef(null);
  const fitAddon = useRef(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentDirectory, setCurrentDirectory] = useState(initialDirectory);
  const [commandHistory, setCommandHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [currentCommand, setCurrentCommand] = useState('');

  // Initialize terminal
  useEffect(() => {
    if (terminalRef.current && !terminalInstance.current) {
      // Create terminal instance
      const terminal = new Terminal({
        theme: {
          background: '#1a1a1a',
          foreground: '#ffffff',
          cursor: '#ffffff',
          selection: '#3e4451',
          black: '#000000',
          red: '#e06c75',
          green: '#98c379',
          yellow: '#e5c07b',
          blue: '#61afef',
          magenta: '#c678dd',
          cyan: '#56b6c2',
          white: '#ffffff',
          brightBlack: '#5c6370',
          brightRed: '#e06c75',
          brightGreen: '#98c379',
          brightYellow: '#e5c07b',
          brightBlue: '#61afef',
          brightMagenta: '#c678dd',
          brightCyan: '#56b6c2',
          brightWhite: '#ffffff'
        },
        fontFamily: '"Fira Code", "Cascadia Code", "JetBrains Mono", monospace',
        fontSize: 14,
        lineHeight: 1.2,
        cursorBlink: true,
        cursorStyle: 'block',
        scrollback: 1000,
        tabStopWidth: 4
      });

      // Create addons
      fitAddon.current = new FitAddon();
      const webLinksAddon = new WebLinksAddon();

      // Load addons
      terminal.loadAddon(fitAddon.current);
      terminal.loadAddon(webLinksAddon);

      // Open terminal
      terminal.open(terminalRef.current);
      fitAddon.current.fit();

      // Store reference
      terminalInstance.current = terminal;

      // Welcome message
      terminal.writeln('\x1b[1;32m╭─────────────────────────────────────╮\x1b[0m');
      terminal.writeln('\x1b[1;32m│     🚀 NORYON AI Development       │\x1b[0m');
      terminal.writeln('\x1b[1;32m│        Web Terminal v1.0           │\x1b[0m');
      terminal.writeln('\x1b[1;32m╰─────────────────────────────────────╯\x1b[0m');
      terminal.writeln('');
      terminal.writeln('\x1b[1;36mWelcome to the AI-powered development environment!\x1b[0m');
      terminal.writeln('\x1b[0;37mType \x1b[1;33mhelp\x1b[0;37m for available commands.\x1b[0m');
      terminal.writeln('');

      // Show prompt
      showPrompt();

      // Handle input
      let currentLine = '';
      terminal.onData((data) => {
        const code = data.charCodeAt(0);

        if (code === 13) { // Enter
          terminal.writeln('');
          if (currentLine.trim()) {
            executeCommand(currentLine.trim());
            setCommandHistory(prev => [...prev, currentLine.trim()]);
            setHistoryIndex(-1);
          }
          currentLine = '';
          showPrompt();
        } else if (code === 127) { // Backspace
          if (currentLine.length > 0) {
            currentLine = currentLine.slice(0, -1);
            terminal.write('\b \b');
          }
        } else if (code === 27) { // Escape sequences (arrow keys)
          // Handle arrow keys for command history
          terminal.onData((escData) => {
            if (escData === '[A') { // Up arrow
              if (commandHistory.length > 0) {
                const newIndex = Math.min(historyIndex + 1, commandHistory.length - 1);
                if (newIndex >= 0) {
                  setHistoryIndex(newIndex);
                  const command = commandHistory[commandHistory.length - 1 - newIndex];
                  // Clear current line and write command
                  terminal.write('\r\x1b[K');
                  showPrompt();
                  terminal.write(command);
                  currentLine = command;
                }
              }
            } else if (escData === '[B') { // Down arrow
              if (historyIndex > 0) {
                const newIndex = historyIndex - 1;
                setHistoryIndex(newIndex);
                const command = commandHistory[commandHistory.length - 1 - newIndex];
                // Clear current line and write command
                terminal.write('\r\x1b[K');
                showPrompt();
                terminal.write(command);
                currentLine = command;
              } else if (historyIndex === 0) {
                setHistoryIndex(-1);
                terminal.write('\r\x1b[K');
                showPrompt();
                currentLine = '';
              }
            }
          });
        } else if (code >= 32) { // Printable characters
          currentLine += data;
          terminal.write(data);
        }
      });
    }

    return () => {
      if (terminalInstance.current) {
        terminalInstance.current.dispose();
        terminalInstance.current = null;
      }
    };
  }, []);

  // Show command prompt
  const showPrompt = useCallback(() => {
    if (terminalInstance.current) {
      const prompt = `\x1b[1;34m┌─[\x1b[1;32muser\x1b[1;34m@\x1b[1;32mnoryon\x1b[1;34m]\x1b[0m\x1b[1;34m─[\x1b[1;37m${currentDirectory}\x1b[1;34m]\x1b[0m\n\x1b[1;34m└─\x1b[1;37m$\x1b[0m `;
      terminalInstance.current.write(prompt);
    }
  }, [currentDirectory]);

  // Execute command
  const executeCommand = useCallback(async (command) => {
    const terminal = terminalInstance.current;
    if (!terminal) return;

    const args = command.split(' ');
    const cmd = args[0].toLowerCase();

    switch (cmd) {
      case 'help':
        terminal.writeln('\x1b[1;33mAvailable Commands:\x1b[0m');
        terminal.writeln('  \x1b[1;32mhelp\x1b[0m          - Show this help message');
        terminal.writeln('  \x1b[1;32mclear\x1b[0m         - Clear the terminal');
        terminal.writeln('  \x1b[1;32mls\x1b[0m            - List files and directories');
        terminal.writeln('  \x1b[1;32mcd <dir>\x1b[0m      - Change directory');
        terminal.writeln('  \x1b[1;32mpwd\x1b[0m           - Print working directory');
        terminal.writeln('  \x1b[1;32mnpm <cmd>\x1b[0m     - Run npm commands');
        terminal.writeln('  \x1b[1;32myarn <cmd>\x1b[0m    - Run yarn commands');
        terminal.writeln('  \x1b[1;32mgit <cmd>\x1b[0m     - Run git commands');
        terminal.writeln('  \x1b[1;32mnode <file>\x1b[0m   - Run Node.js file');
        terminal.writeln('  \x1b[1;32mcat <file>\x1b[0m    - Display file contents');
        terminal.writeln('  \x1b[1;32mtouch <file>\x1b[0m  - Create new file');
        terminal.writeln('  \x1b[1;32mmkdir <dir>\x1b[0m   - Create directory');
        terminal.writeln('  \x1b[1;32mecho <text>\x1b[0m   - Print text');
        break;

      case 'clear':
        terminal.clear();
        break;

      case 'ls':
        terminal.writeln('\x1b[1;34mDirectories:\x1b[0m');
        terminal.writeln('  \x1b[1;36msrc/\x1b[0m');
        terminal.writeln('  \x1b[1;36mpublic/\x1b[0m');
        terminal.writeln('  \x1b[1;36mnode_modules/\x1b[0m');
        terminal.writeln('');
        terminal.writeln('\x1b[1;33mFiles:\x1b[0m');
        terminal.writeln('  \x1b[0;37mpackage.json\x1b[0m');
        terminal.writeln('  \x1b[0;37mvite.config.js\x1b[0m');
        terminal.writeln('  \x1b[0;37mREADME.md\x1b[0m');
        break;

      case 'pwd':
        terminal.writeln(currentDirectory);
        break;

      case 'cd':
        if (args[1]) {
          if (args[1] === '..') {
            const parts = currentDirectory.split('/').filter(p => p);
            parts.pop();
            setCurrentDirectory('/' + parts.join('/'));
          } else if (args[1].startsWith('/')) {
            setCurrentDirectory(args[1]);
          } else {
            setCurrentDirectory(currentDirectory + '/' + args[1]);
          }
          terminal.writeln(`Changed directory to: ${args[1]}`);
        } else {
          terminal.writeln('Usage: cd <directory>');
        }
        break;

      case 'npm':
      case 'yarn':
      case 'git':
      case 'node':
        terminal.writeln(`\x1b[1;33mExecuting:\x1b[0m ${command}`);
        terminal.writeln('\x1b[0;90m[Simulated command execution]\x1b[0m');
        
        // Simulate command execution
        if (onCommand) {
          try {
            const result = await onCommand(command, currentDirectory);
            if (result) {
              terminal.writeln(result);
            }
          } catch (error) {
            terminal.writeln(`\x1b[1;31mError:\x1b[0m ${error.message}`);
          }
        } else {
          terminal.writeln('\x1b[1;32mCommand completed successfully\x1b[0m');
        }
        break;

      case 'cat':
        if (args[1]) {
          terminal.writeln(`\x1b[1;33mContents of ${args[1]}:\x1b[0m`);
          terminal.writeln('\x1b[0;90m[File contents would be displayed here]\x1b[0m');
        } else {
          terminal.writeln('Usage: cat <filename>');
        }
        break;

      case 'touch':
        if (args[1]) {
          terminal.writeln(`\x1b[1;32mCreated file:\x1b[0m ${args[1]}`);
        } else {
          terminal.writeln('Usage: touch <filename>');
        }
        break;

      case 'mkdir':
        if (args[1]) {
          terminal.writeln(`\x1b[1;32mCreated directory:\x1b[0m ${args[1]}`);
        } else {
          terminal.writeln('Usage: mkdir <directory>');
        }
        break;

      case 'echo':
        terminal.writeln(args.slice(1).join(' '));
        break;

      default:
        terminal.writeln(`\x1b[1;31mCommand not found:\x1b[0m ${cmd}`);
        terminal.writeln('Type \x1b[1;33mhelp\x1b[0m for available commands.');
    }
  }, [currentDirectory, onCommand]);

  // Handle resize
  const handleResize = useCallback(() => {
    if (fitAddon.current) {
      fitAddon.current.fit();
    }
  }, []);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
    setTimeout(handleResize, 100);
  }, [handleResize]);

  // Clear terminal
  const clearTerminal = useCallback(() => {
    if (terminalInstance.current) {
      terminalInstance.current.clear();
      showPrompt();
    }
  }, [showPrompt]);

  // Handle window resize
  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  return (
    <div className={`flex flex-col h-full bg-gray-900 ${className} ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* Terminal Header */}
      <div className="flex items-center justify-between p-2 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <TerminalIcon className="w-4 h-4 text-green-500" />
          <span className="text-sm text-gray-300">{title}</span>
          <Badge variant="outline" className="text-xs">
            {currentDirectory}
          </Badge>
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={clearTerminal}
            className="h-7 px-2"
            title="Clear Terminal"
          >
            <RotateCcw className="w-3 h-3" />
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={toggleFullscreen}
            className="h-7 px-2"
            title="Toggle Fullscreen"
          >
            {isFullscreen ? (
              <Minimize2 className="w-3 h-3" />
            ) : (
              <Maximize2 className="w-3 h-3" />
            )}
          </Button>
        </div>
      </div>

      {/* Terminal Content */}
      <div className="flex-1 p-2">
        <div 
          ref={terminalRef} 
          className="w-full h-full"
          style={{ minHeight: '200px' }}
        />
      </div>
    </div>
  );
};

export default WebTerminal;
