import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebLinksAddon } from 'xterm-addon-web-links';
import { 
  Terminal as TerminalIcon, 
  X, 
  Maximize2, 
  Minimize2, 
  RotateCcw,
  Settings,
  Plus
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import 'xterm/css/xterm.css';

const WebTerminal = ({
  onCommand,
  className = '',
  initialDirectory = '/',
  title = 'Terminal'
}) => {
  const terminalRef = useRef(null);
  const terminalInstance = useRef(null);
  const fitAddon = useRef(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentDirectory, setCurrentDirectory] = useState(initialDirectory);
  const [commandHistory, setCommandHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [currentCommand, setCurrentCommand] = useState('');

  // Initialize terminal
  useEffect(() => {
    if (terminalRef.current && !terminalInstance.current) {
      // Create terminal instance
      const terminal = new Terminal({
        theme: {
          background: '#1a1a1a',
          foreground: '#ffffff',
          cursor: '#ffffff',
          selection: '#3e4451',
          black: '#000000',
          red: '#e06c75',
          green: '#98c379',
          yellow: '#e5c07b',
          blue: '#61afef',
          magenta: '#c678dd',
          cyan: '#56b6c2',
          white: '#ffffff',
          brightBlack: '#5c6370',
          brightRed: '#e06c75',
          brightGreen: '#98c379',
          brightYellow: '#e5c07b',
          brightBlue: '#61afef',
          brightMagenta: '#c678dd',
          brightCyan: '#56b6c2',
          brightWhite: '#ffffff'
        },
        fontFamily: '"Fira Code", "Cascadia Code", "JetBrains Mono", monospace',
        fontSize: 14,
        lineHeight: 1.2,
        cursorBlink: true,
        cursorStyle: 'block',
        scrollback: 1000,
        tabStopWidth: 4
      });

      // Create addons
      fitAddon.current = new FitAddon();
      const webLinksAddon = new WebLinksAddon();

      // Load addons
      terminal.loadAddon(fitAddon.current);
      terminal.loadAddon(webLinksAddon);

      // Open terminal
      terminal.open(terminalRef.current);
      fitAddon.current.fit();

      // Store reference
      terminalInstance.current = terminal;

      // Welcome message
      terminal.writeln('\x1b[1;32m╭─────────────────────────────────────╮\x1b[0m');
      terminal.writeln('\x1b[1;32m│     🚀 NORYON AI Development       │\x1b[0m');
      terminal.writeln('\x1b[1;32m│        Web Terminal v1.0           │\x1b[0m');
      terminal.writeln('\x1b[1;32m╰─────────────────────────────────────╯\x1b[0m');
      terminal.writeln('');
      terminal.writeln('\x1b[1;36mWelcome to the AI-powered development environment!\x1b[0m');
      terminal.writeln('\x1b[0;37mType \x1b[1;33mhelp\x1b[0;37m for available commands.\x1b[0m');
      terminal.writeln('');

      // Show prompt
      showPrompt();

      // Handle input
      let currentLine = '';
      terminal.onData((data) => {
        const code = data.charCodeAt(0);

        if (code === 13) { // Enter
          terminal.writeln('');
          if (currentLine.trim()) {
            executeCommand(currentLine.trim());
            setCommandHistory(prev => [...prev, currentLine.trim()]);
            setHistoryIndex(-1);
          }
          currentLine = '';
          showPrompt();
        } else if (code === 127) { // Backspace
          if (currentLine.length > 0) {
            currentLine = currentLine.slice(0, -1);
            terminal.write('\b \b');
          }
        } else if (code === 27) { // Escape sequences (arrow keys)
          // Handle arrow keys for command history
          terminal.onData((escData) => {
            if (escData === '[A') { // Up arrow
              if (commandHistory.length > 0) {
                const newIndex = Math.min(historyIndex + 1, commandHistory.length - 1);
                if (newIndex >= 0) {
                  setHistoryIndex(newIndex);
                  const command = commandHistory[commandHistory.length - 1 - newIndex];
                  // Clear current line and write command
                  terminal.write('\r\x1b[K');
                  showPrompt();
                  terminal.write(command);
                  currentLine = command;
                }
              }
            } else if (escData === '[B') { // Down arrow
              if (historyIndex > 0) {
                const newIndex = historyIndex - 1;
                setHistoryIndex(newIndex);
                const command = commandHistory[commandHistory.length - 1 - newIndex];
                // Clear current line and write command
                terminal.write('\r\x1b[K');
                showPrompt();
                terminal.write(command);
                currentLine = command;
              } else if (historyIndex === 0) {
                setHistoryIndex(-1);
                terminal.write('\r\x1b[K');
                showPrompt();
                currentLine = '';
              }
            }
          });
        } else if (code >= 32) { // Printable characters
          currentLine += data;
          terminal.write(data);
        }
      });
    }

    return () => {
      if (terminalInstance.current) {
        terminalInstance.current.dispose();
        terminalInstance.current = null;
      }
    };
  }, []);

  // Show command prompt
  const showPrompt = useCallback(() => {
    if (terminalInstance.current) {
      const prompt = `\x1b[1;34m┌─[\x1b[1;32muser\x1b[1;34m@\x1b[1;32mnoryon\x1b[1;34m]\x1b[0m\x1b[1;34m─[\x1b[1;37m${currentDirectory}\x1b[1;34m]\x1b[0m\n\x1b[1;34m└─\x1b[1;37m$\x1b[0m `;
      terminalInstance.current.write(prompt);
    }
  }, [currentDirectory]);

  // Execute command
  const executeCommand = useCallback(async (command) => {
    const terminal = terminalInstance.current;
    if (!terminal) return;

    const args = command.split(' ');
    const cmd = args[0].toLowerCase();

    switch (cmd) {
      case 'help':
        terminal.writeln('\x1b[1;33m🚀 NORYON AI Terminal - Available Commands:\x1b[0m');
        terminal.writeln('');
        terminal.writeln('\x1b[1;36m📁 File Operations:\x1b[0m');
        terminal.writeln('  \x1b[1;32mls\x1b[0m            - List files and directories');
        terminal.writeln('  \x1b[1;32mcd <dir>\x1b[0m      - Change directory');
        terminal.writeln('  \x1b[1;32mpwd\x1b[0m           - Print working directory');
        terminal.writeln('  \x1b[1;32mcat <file>\x1b[0m    - Display file contents');
        terminal.writeln('  \x1b[1;32mtouch <file>\x1b[0m  - Create new file');
        terminal.writeln('  \x1b[1;32mmkdir <dir>\x1b[0m   - Create directory');
        terminal.writeln('  \x1b[1;32mrm <file>\x1b[0m     - Remove file');
        terminal.writeln('');
        terminal.writeln('\x1b[1;36m📦 Package Management:\x1b[0m');
        terminal.writeln('  \x1b[1;32mnpm install\x1b[0m   - Install dependencies');
        terminal.writeln('  \x1b[1;32mnpm run dev\x1b[0m   - Start development server');
        terminal.writeln('  \x1b[1;32mnpm run build\x1b[0m - Build for production');
        terminal.writeln('  \x1b[1;32myarn <cmd>\x1b[0m    - Run yarn commands');
        terminal.writeln('');
        terminal.writeln('\x1b[1;36m🔧 Development:\x1b[0m');
        terminal.writeln('  \x1b[1;32mgit <cmd>\x1b[0m     - Git version control');
        terminal.writeln('  \x1b[1;32mnode <file>\x1b[0m   - Run Node.js file');
        terminal.writeln('  \x1b[1;32mserve\x1b[0m         - Start local server');
        terminal.writeln('');
        terminal.writeln('\x1b[1;36m🤖 AI Commands:\x1b[0m');
        terminal.writeln('  \x1b[1;32mai generate\x1b[0m   - Generate code with AI');
        terminal.writeln('  \x1b[1;32mai fix\x1b[0m        - Fix code issues');
        terminal.writeln('  \x1b[1;32mai explain\x1b[0m    - Explain code');
        terminal.writeln('');
        terminal.writeln('\x1b[1;36m⚡ Utilities:\x1b[0m');
        terminal.writeln('  \x1b[1;32mclear\x1b[0m         - Clear the terminal');
        terminal.writeln('  \x1b[1;32mecho <text>\x1b[0m   - Print text');
        terminal.writeln('  \x1b[1;32mdate\x1b[0m          - Show current date/time');
        terminal.writeln('  \x1b[1;32mwhoami\x1b[0m        - Show current user');
        break;

      case 'clear':
        terminal.clear();
        break;

      case 'ls':
        // Show actual project files if available
        if (onCommand) {
          try {
            const result = await onCommand(command, currentDirectory);
            if (result) {
              terminal.writeln(result);
            } else {
              // Fallback to simulated listing
              terminal.writeln('\x1b[1;34m📁 Directories:\x1b[0m');
              terminal.writeln('  \x1b[1;36msrc/\x1b[0m          - Source code files');
              terminal.writeln('  \x1b[1;36mpublic/\x1b[0m       - Static assets');
              terminal.writeln('  \x1b[1;36mnode_modules/\x1b[0m - Dependencies');
              terminal.writeln('');
              terminal.writeln('\x1b[1;33m📄 Files:\x1b[0m');
              terminal.writeln('  \x1b[0;37mpackage.json\x1b[0m   - Project configuration');
              terminal.writeln('  \x1b[0;37mvite.config.js\x1b[0m - Build configuration');
              terminal.writeln('  \x1b[0;37mindex.html\x1b[0m     - Main HTML file');
              terminal.writeln('  \x1b[0;37mREADME.md\x1b[0m      - Project documentation');
            }
          } catch (error) {
            terminal.writeln(`\x1b[1;31mError:\x1b[0m ${error.message}`);
          }
        } else {
          // Default listing
          terminal.writeln('\x1b[1;34m📁 Project Structure:\x1b[0m');
          terminal.writeln('  \x1b[1;36msrc/\x1b[0m');
          terminal.writeln('  \x1b[1;36mpublic/\x1b[0m');
          terminal.writeln('  \x1b[0;37mpackage.json\x1b[0m');
          terminal.writeln('  \x1b[0;37mindex.html\x1b[0m');
        }
        break;

      case 'pwd':
        terminal.writeln(currentDirectory);
        break;

      case 'cd':
        if (args[1]) {
          if (args[1] === '..') {
            const parts = currentDirectory.split('/').filter(p => p);
            parts.pop();
            setCurrentDirectory('/' + parts.join('/'));
          } else if (args[1].startsWith('/')) {
            setCurrentDirectory(args[1]);
          } else {
            setCurrentDirectory(currentDirectory + '/' + args[1]);
          }
          terminal.writeln(`Changed directory to: ${args[1]}`);
        } else {
          terminal.writeln('Usage: cd <directory>');
        }
        break;

      case 'npm':
        await handleNpmCommand(args, terminal);
        break;

      case 'yarn':
        await handleYarnCommand(args, terminal);
        break;

      case 'git':
        await handleGitCommand(args, terminal);
        break;

      case 'node':
        await handleNodeCommand(args, terminal);
        break;

      case 'ai':
        await handleAICommand(args, terminal);
        break;

      case 'serve':
        terminal.writeln('\x1b[1;33m🚀 Starting local development server...\x1b[0m');
        terminal.writeln('\x1b[1;32m✓ Server running at http://localhost:3000\x1b[0m');
        terminal.writeln('\x1b[0;37m  Use the preview panel to see your website\x1b[0m');
        break;

      case 'date':
        terminal.writeln(new Date().toString());
        break;

      case 'whoami':
        terminal.writeln('\x1b[1;36mdeveloper@noryon-ai\x1b[0m');
        break;

      case 'cat':
        if (args[1]) {
          terminal.writeln(`\x1b[1;33mContents of ${args[1]}:\x1b[0m`);
          terminal.writeln('\x1b[0;90m[File contents would be displayed here]\x1b[0m');
        } else {
          terminal.writeln('Usage: cat <filename>');
        }
        break;

      case 'touch':
        if (args[1]) {
          terminal.writeln(`\x1b[1;32mCreated file:\x1b[0m ${args[1]}`);
        } else {
          terminal.writeln('Usage: touch <filename>');
        }
        break;

      case 'mkdir':
        if (args[1]) {
          terminal.writeln(`\x1b[1;32mCreated directory:\x1b[0m ${args[1]}`);
        } else {
          terminal.writeln('Usage: mkdir <directory>');
        }
        break;

      case 'echo':
        terminal.writeln(args.slice(1).join(' '));
        break;

      default:
        terminal.writeln(`\x1b[1;31mCommand not found:\x1b[0m ${cmd}`);
        terminal.writeln('Type \x1b[1;33mhelp\x1b[0m for available commands.');
    }
  }, [currentDirectory, onCommand]);

  // Handle NPM commands
  const handleNpmCommand = useCallback(async (args, terminal) => {
    const subCommand = args[1];

    switch (subCommand) {
      case 'install':
      case 'i':
        terminal.writeln('\x1b[1;33m📦 Installing dependencies...\x1b[0m');
        await simulateProgress(terminal, [
          'Resolving packages...',
          'Downloading packages...',
          'Installing packages...',
          'Building dependencies...'
        ]);
        terminal.writeln('\x1b[1;32m✓ Dependencies installed successfully!\x1b[0m');
        break;

      case 'run':
        const script = args[2];
        if (script === 'dev') {
          terminal.writeln('\x1b[1;33m🚀 Starting development server...\x1b[0m');
          terminal.writeln('\x1b[1;32m✓ Server running at http://localhost:3000\x1b[0m');
          terminal.writeln('\x1b[0;37m  Local:   http://localhost:3000/\x1b[0m');
          terminal.writeln('\x1b[0;37m  Network: use --host to expose\x1b[0m');
        } else if (script === 'build') {
          terminal.writeln('\x1b[1;33m🏗️  Building for production...\x1b[0m');
          await simulateProgress(terminal, [
            'Optimizing assets...',
            'Minifying code...',
            'Generating bundles...'
          ]);
          terminal.writeln('\x1b[1;32m✓ Build completed! Check dist/ folder\x1b[0m');
        } else {
          terminal.writeln(`\x1b[1;31mScript "${script}" not found\x1b[0m`);
        }
        break;

      default:
        terminal.writeln(`\x1b[1;33mRunning:\x1b[0m npm ${args.slice(1).join(' ')}`);
        terminal.writeln('\x1b[1;32m✓ Command completed\x1b[0m');
    }
  }, []);

  // Handle Yarn commands
  const handleYarnCommand = useCallback(async (args, terminal) => {
    const subCommand = args[1];

    switch (subCommand) {
      case 'install':
      case undefined:
        terminal.writeln('\x1b[1;33m🧶 Installing with Yarn...\x1b[0m');
        await simulateProgress(terminal, [
          'Resolving packages...',
          'Fetching packages...',
          'Linking dependencies...'
        ]);
        terminal.writeln('\x1b[1;32m✓ Done in 2.34s\x1b[0m');
        break;

      case 'dev':
        terminal.writeln('\x1b[1;33m🚀 Starting Yarn dev server...\x1b[0m');
        terminal.writeln('\x1b[1;32m✓ Server running at http://localhost:3000\x1b[0m');
        break;

      default:
        terminal.writeln(`\x1b[1;33mRunning:\x1b[0m yarn ${args.slice(1).join(' ')}`);
        terminal.writeln('\x1b[1;32m✓ Command completed\x1b[0m');
    }
  }, []);

  // Handle Git commands
  const handleGitCommand = useCallback(async (args, terminal) => {
    const subCommand = args[1];

    switch (subCommand) {
      case 'status':
        terminal.writeln('\x1b[1;33mOn branch main\x1b[0m');
        terminal.writeln('Your branch is up to date with \'origin/main\'.');
        terminal.writeln('');
        terminal.writeln('\x1b[1;32mChanges to be committed:\x1b[0m');
        terminal.writeln('  \x1b[1;32mmodified:   src/App.jsx\x1b[0m');
        terminal.writeln('  \x1b[1;32mnew file:   src/components/NewComponent.jsx\x1b[0m');
        break;

      case 'add':
        terminal.writeln(`\x1b[1;32m✓ Added ${args.slice(2).join(' ') || 'files'} to staging\x1b[0m`);
        break;

      case 'commit':
        terminal.writeln('\x1b[1;33mCommitting changes...\x1b[0m');
        terminal.writeln('\x1b[1;32m✓ [main abc1234] Your commit message\x1b[0m');
        terminal.writeln(' 2 files changed, 15 insertions(+), 3 deletions(-)');
        break;

      case 'push':
        terminal.writeln('\x1b[1;33mPushing to origin/main...\x1b[0m');
        terminal.writeln('\x1b[1;32m✓ Everything up-to-date\x1b[0m');
        break;

      case 'pull':
        terminal.writeln('\x1b[1;33mPulling from origin/main...\x1b[0m');
        terminal.writeln('\x1b[1;32m✓ Already up to date.\x1b[0m');
        break;

      default:
        terminal.writeln(`\x1b[1;33mRunning:\x1b[0m git ${args.slice(1).join(' ')}`);
        terminal.writeln('\x1b[1;32m✓ Git command completed\x1b[0m');
    }
  }, []);

  // Handle Node commands
  const handleNodeCommand = useCallback(async (args, terminal) => {
    const fileName = args[1];
    if (!fileName) {
      terminal.writeln('\x1b[1;31mUsage: node <filename>\x1b[0m');
      return;
    }

    terminal.writeln(`\x1b[1;33mRunning:\x1b[0m node ${fileName}`);
    terminal.writeln('\x1b[1;32m✓ Script executed successfully\x1b[0m');
  }, []);

  // Handle AI commands
  const handleAICommand = useCallback(async (args, terminal) => {
    const subCommand = args[1];

    switch (subCommand) {
      case 'generate':
        terminal.writeln('\x1b[1;33m🤖 AI Code Generation\x1b[0m');
        terminal.writeln('Use the AI Assistant panel to generate code with natural language!');
        terminal.writeln('Example: "Create a modern portfolio website"');
        break;

      case 'fix':
        terminal.writeln('\x1b[1;33m🔧 AI Code Fixing\x1b[0m');
        terminal.writeln('Select code in the editor and use AI Assistant to fix issues!');
        break;

      case 'explain':
        terminal.writeln('\x1b[1;33m📖 AI Code Explanation\x1b[0m');
        terminal.writeln('Ask the AI Assistant to explain any code you\'re working on!');
        break;

      default:
        terminal.writeln('\x1b[1;31mAI commands: generate, fix, explain\x1b[0m');
    }
  }, []);

  // Simulate progress for long-running commands
  const simulateProgress = useCallback(async (terminal, steps) => {
    for (let i = 0; i < steps.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 800));
      terminal.writeln(`\x1b[0;90m${steps[i]}\x1b[0m`);
    }
  }, []);

  // Handle resize
  const handleResize = useCallback(() => {
    if (fitAddon.current) {
      fitAddon.current.fit();
    }
  }, []);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev);
    setTimeout(handleResize, 100);
  }, [handleResize]);

  // Clear terminal
  const clearTerminal = useCallback(() => {
    if (terminalInstance.current) {
      terminalInstance.current.clear();
      showPrompt();
    }
  }, [showPrompt]);

  // Handle window resize
  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  return (
    <div className={`flex flex-col h-full bg-gray-900 ${className} ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}>
      {/* Terminal Header */}
      <div className="flex items-center justify-between p-2 bg-gray-800 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <TerminalIcon className="w-4 h-4 text-green-500" />
          <span className="text-sm text-gray-300">{title}</span>
          <Badge variant="outline" className="text-xs">
            {currentDirectory}
          </Badge>
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={clearTerminal}
            className="h-7 px-2"
            title="Clear Terminal"
          >
            <RotateCcw className="w-3 h-3" />
          </Button>
          
          <Button
            size="sm"
            variant="ghost"
            onClick={toggleFullscreen}
            className="h-7 px-2"
            title="Toggle Fullscreen"
          >
            {isFullscreen ? (
              <Minimize2 className="w-3 h-3" />
            ) : (
              <Maximize2 className="w-3 h-3" />
            )}
          </Button>
        </div>
      </div>

      {/* Terminal Content */}
      <div className="flex-1 p-2">
        <div 
          ref={terminalRef} 
          className="w-full h-full"
          style={{ minHeight: '200px' }}
        />
      </div>
    </div>
  );
};

export default WebTerminal;
