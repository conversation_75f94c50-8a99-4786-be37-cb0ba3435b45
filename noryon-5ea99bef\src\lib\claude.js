import Anthropic from '@anthropic-ai/sdk'

// Resolve API key from Vite (browser) or Node environment variables
const apiKey = (typeof import.meta !== 'undefined' && import.meta.env && import.meta.env.VITE_ANTHROPIC_API_KEY) ||
               process.env.ANTHROPIC_API_KEY ||
               process.env.VITE_ANTHROPIC_API_KEY;

if (!apiKey) {
  console.warn('Claude API key is missing. Set VITE_ANTHROPIC_API_KEY or ANTHROPIC_API_KEY in your environment.');
}

const anthropic = new Anthropic({
  apiKey,
  // Only allow browser usage during development; Node backend ignores this flag
  dangerouslyAllowBrowser: true
})

export { anthropic }