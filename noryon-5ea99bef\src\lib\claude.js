import Anthropic from '@anthropic-ai/sdk'

// Function to get API key (lazy evaluation)
function getApiKey() {
  return (typeof import.meta !== 'undefined' && import.meta.env && import.meta.env.VITE_ANTHROPIC_API_KEY) ||
         process.env.VITE_ANTHROPIC_API_KEY ||
         process.env.ANTHROPIC_API_KEY;
}

// Function to get Anthropic client (lazy initialization)
function getAnthropic() {
  const apiKey = getApiKey();

  if (!apiKey) {
    throw new Error('Claude API key is missing. Set VITE_ANTHROPIC_API_KEY or ANTHROPIC_API_KEY in your environment.');
  }

  return new Anthropic({
    apiKey,
    // Only allow browser usage during development; Node backend ignores this flag
    dangerouslyAllowBrowser: true
  });
}

// Export a proxy object that creates the client when needed
export const anthropic = {
  get messages() {
    return getAnthropic().messages;
  }
}