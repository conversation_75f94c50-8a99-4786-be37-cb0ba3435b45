import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { 
  ArrowLeft, 
  Save, 
  Play, 
  Share, 
  Download,
  Setting<PERSON>,
  Bot,
  Zap
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import IDELayout from '@/components/ide/IDELayout';
import { generateWebAppCode } from '@/api/webCodeGeneration';
import { InvokeLLM } from '@/api/llm';

const CodeIDE = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [projectName, setProjectName] = useState('Untitled Project');
  const [fileStructure, setFileStructure] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);

  // Initialize with sample project or load from URL params
  useEffect(() => {
    const projectId = searchParams.get('project');
    const loadSample = searchParams.get('sample');
    
    if (projectId) {
      // Load existing project
      loadProject(projectId);
    } else if (loadSample) {
      // Load sample project
      loadSampleProject();
    } else {
      // Create new project
      createNewProject();
    }
  }, [searchParams]);

  // Create new project with basic structure
  const createNewProject = useCallback(() => {
    const initialFiles = {
      'package.json': JSON.stringify({
        name: 'my-web-app',
        version: '1.0.0',
        type: 'module',
        scripts: {
          dev: 'vite',
          build: 'vite build',
          preview: 'vite preview'
        },
        dependencies: {
          react: '^18.2.0',
          'react-dom': '^18.2.0'
        },
        devDependencies: {
          '@vitejs/plugin-react': '^4.0.0',
          vite: '^4.4.0'
        }
      }, null, 2),
      'index.html': `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>My Web App</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>`,
      'src/main.jsx': `import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.jsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)`,
      'src/App.jsx': `import React, { useState } from 'react'
import './App.css'

function App() {
  const [count, setCount] = useState(0)

  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to My Web App</h1>
        <div className="card">
          <button onClick={() => setCount((count) => count + 1)}>
            count is {count}
          </button>
          <p>
            Edit <code>src/App.jsx</code> and save to test HMR
          </p>
        </div>
        <p className="read-the-docs">
          Click on the Vite and React logos to learn more
        </p>
      </header>
    </div>
  )
}

export default App`,
      'src/App.css': `#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  border-radius: 8px;
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  color: white;
  cursor: pointer;
  transition: border-color 0.25s;
}

button:hover {
  border-color: #646cff;
}`,
      'src/index.css': `body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;
}

#root {
  width: 100%;
}`,
      'vite.config.js': `import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true
  }
})`
    };

    setFileStructure(initialFiles);
    setProjectName('New Web App');
  }, []);

  // Load sample project
  const loadSampleProject = useCallback(() => {
    // Load from localStorage if available
    const savedProject = localStorage.getItem('latest_web_file_tree');
    if (savedProject) {
      try {
        const parsed = JSON.parse(savedProject);
        setFileStructure(parsed);
        setProjectName('Generated Web App');
        return;
      } catch (error) {
        console.error('Failed to load saved project:', error);
      }
    }
    
    // Fallback to new project
    createNewProject();
  }, [createNewProject]);

  // Load existing project
  const loadProject = useCallback(async (projectId) => {
    setIsLoading(true);
    try {
      // Simulate loading project from backend
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // For now, load sample project
      loadSampleProject();
      
      toast.success('Project loaded successfully');
    } catch (error) {
      console.error('Failed to load project:', error);
      toast.error('Failed to load project');
      createNewProject();
    } finally {
      setIsLoading(false);
    }
  }, [loadSampleProject, createNewProject]);

  // Handle files change
  const handleFilesChange = useCallback((newFiles) => {
    setFileStructure(newFiles);
  }, []);

  // Handle AI assist
  const handleAIAssist = useCallback(async (context) => {
    try {
      const { selectedText, fullCode, fileName, cursorPosition } = context;
      
      let prompt = '';
      if (selectedText) {
        prompt = `Help me improve this code:\n\n${selectedText}\n\nContext: This is from ${fileName}`;
      } else {
        prompt = `Help me with this ${fileName} file. Current code:\n\n${fullCode.slice(0, 1000)}${fullCode.length > 1000 ? '...' : ''}`;
      }

      toast.info('AI is analyzing your code...');
      
      const response = await InvokeLLM.invoke(prompt, { maxTokens: 1000 });
      
      // For now, just show the response in a toast
      // In a real implementation, you'd show this in a proper AI assistant panel
      console.log('AI Response:', response);
      toast.success('AI analysis complete! Check the console for suggestions.');
      
    } catch (error) {
      console.error('AI assist failed:', error);
      toast.error('AI assist failed. Please try again.');
    }
  }, []);

  // Handle run code
  const handleRunCode = useCallback((code, fileName, allFiles) => {
    toast.info(`Running ${fileName}...`);
    
    // Simulate code execution
    setTimeout(() => {
      toast.success('Code executed successfully!');
    }, 1000);
  }, []);

  // Save project
  const handleSave = useCallback(async () => {
    setIsSaving(true);
    try {
      // Save to localStorage for now
      localStorage.setItem('latest_web_file_tree', JSON.stringify(fileStructure));
      localStorage.setItem('project_name', projectName);
      
      setLastSaved(new Date());
      toast.success('Project saved successfully');
    } catch (error) {
      console.error('Failed to save project:', error);
      toast.error('Failed to save project');
    } finally {
      setIsSaving(false);
    }
  }, [fileStructure, projectName]);

  // Generate with AI
  const handleAIGenerate = useCallback(async () => {
    const prompt = window.prompt('Describe what you want to build:');
    if (!prompt) return;

    setIsLoading(true);
    try {
      toast.info('AI is generating your code...');
      
      const generatedFiles = await generateWebAppCode(prompt);
      
      if (generatedFiles && generatedFiles.files) {
        // Parse the generated files
        let parsedFiles = {};
        try {
          // The response might be wrapped in a files property
          const filesContent = generatedFiles.files;
          if (typeof filesContent === 'string') {
            // Try to extract JSON from the response
            const jsonMatch = filesContent.match(/```json\n([\s\S]*?)\n```/);
            if (jsonMatch) {
              parsedFiles = JSON.parse(jsonMatch[1]);
            } else {
              // Try to parse the entire response as JSON
              parsedFiles = JSON.parse(filesContent);
            }
          } else {
            parsedFiles = filesContent;
          }
        } catch (parseError) {
          console.error('Failed to parse generated files:', parseError);
          toast.error('Failed to parse generated code');
          return;
        }
        
        setFileStructure(parsedFiles);
        setProjectName('AI Generated App');
        toast.success('Code generated successfully!');
      }
    } catch (error) {
      console.error('AI generation failed:', error);
      toast.error('AI generation failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Export project
  const handleExport = useCallback(() => {
    const dataStr = JSON.stringify(fileStructure, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `${projectName.replace(/\s+/g, '-').toLowerCase()}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
    
    toast.success('Project exported successfully');
  }, [fileStructure, projectName]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-900 text-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Loading IDE...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-900 text-white">
      {/* Top Bar */}
      <div className="h-12 bg-gray-800 border-b border-gray-700 flex items-center justify-between px-4">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(-1)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Back
          </Button>
          
          <div className="flex items-center gap-2">
            <h1 className="text-lg font-semibold">{projectName}</h1>
            {lastSaved && (
              <Badge variant="outline" className="text-xs">
                Saved {lastSaved.toLocaleTimeString()}
              </Badge>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleAIGenerate}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <Bot className="w-4 h-4" />
            AI Generate
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center gap-2"
          >
            <Save className="w-4 h-4" />
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleExport}
            className="flex items-center gap-2"
          >
            <Download className="w-4 h-4" />
            Export
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => toast.info('Share feature coming soon!')}
            className="flex items-center gap-2"
          >
            <Share className="w-4 h-4" />
            Share
          </Button>
        </div>
      </div>

      {/* IDE Content */}
      <div className="flex-1">
        <IDELayout
          initialFiles={fileStructure}
          onFilesChange={handleFilesChange}
          onAIAssist={handleAIAssist}
          onRunCode={handleRunCode}
        />
      </div>
    </div>
  );
};

export default CodeIDE;
