import React, { useState, useEffect } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { Agent } from "@/api/entities";
import { Project } from "@/api/entities";
import { User } from "@/api/entities";
import { generateWebAppCode } from "@/api/webCodeGeneration";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft,
  Sparkles,
  Zap,
  Code,
  Palette,
  Send,
  Clock,
  CheckCircle,
  Globe,
  Download,
  AlertCircle,
  Copy
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { createPageUrl } from "@/utils";

export default function CreateWithAgent() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const agentId = searchParams.get('agent_id');
  
  const [agent, setAgent] = useState(null);
  const [user, setUser] = useState(null);
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generatedProject, setGeneratedProject] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");

  // Mock fallback data for when real data doesn't exist
  const mockAgentData = {
    "minimalist-portfolio-v2": {
      id: "1",
      agent_id: "minimalist-portfolio-v2",
      name: "Minimalist Portfolio",
      thumbnail_url: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop",
      tags: ["portfolio", "minimalist", "photographer"],
      short_description: "Clean, black-and-white portfolios for creatives",
      category: "portfolio",
      style_tokens: {
        colors: { primary: "#000", background: "#fff", accent: "#ff5a5f" },
        fonts: { base: "Inter, sans-serif", heading: "Playfair Display" },
        spacing: { small: 8, medium: 16, large: 32 }
      },
      components: [
        {
          type: "hero.v2",
          props_schema: { headline: "string", subheadline: "string", cta_text: "string" },
          default_code_ref: "templates/hero/v2.tsx"
        },
        {
          type: "gallery.grid",
          props_schema: { images: "string[]" },
          default_code_ref: "templates/gallery/grid.tsx"
        }
      ],
      example_prompts: [
        "Photographer portfolio with large gallery",
        "Wedding photographer in Portland with contact form",
        "Art portfolio with minimalist black and white design"
      ]
    },
    "modern-business-v1": {
      id: "2",
      agent_id: "modern-business-v1",
      name: "Modern Business",
      thumbnail_url: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop",
      tags: ["business", "corporate", "professional"],
      short_description: "Professional business websites with clean layouts",
      category: "business",
      style_tokens: {
        colors: { primary: "#2563eb", background: "#f8fafc", accent: "#0ea5e9" },
        fonts: { base: "Inter, sans-serif", heading: "Inter, sans-serif" },
        spacing: { small: 8, medium: 16, large: 32 }
      },
      components: [
        {
          type: "hero.business",
          props_schema: { headline: "string", description: "string", cta_primary: "string" },
          default_code_ref: "templates/hero/business.tsx"
        }
      ],
      example_prompts: [
        "Corporate consulting firm with services page",
        "Professional accounting business website",
        "Law firm with attorney profiles and contact"
      ]
    }
  };

  useEffect(() => {
    if (agentId) {
      loadAgent();
      loadUser();
    } else {
      navigate(createPageUrl("AgentGallery"));
    }
  }, [agentId]);

  const loadUser = async () => {
    try {
      const currentUser = await User.me().catch(() => null);
      setUser(currentUser);
    } catch (error) {
      console.error("Error loading user:", error);
    }
  };

  const loadAgent = async () => {
    try {
      setError("");
      
      // Try to load from database first
      const agents = await Agent.list().catch(() => []);
      let foundAgent = agents.find(a => a.agent_id === agentId);
      
      // Fallback to mock data if not found
      if (!foundAgent && mockAgentData[agentId]) {
        foundAgent = mockAgentData[agentId];
      }
      
      if (foundAgent) {
        setAgent(foundAgent);
      } else {
        setError("Agent not found. Please select a different agent.");
      }
    } catch (error) {
      console.error("Error loading agent:", error);
      setError("Failed to load agent. Please try again.");
    }
    setIsLoading(false);
  };

  const handleGenerate = async () => {
    if (!prompt.trim() || !agent || isGenerating) return;
    
    setIsGenerating(true);
    setGenerationProgress(0);
    setError("");
    
    try {
      // Progress simulation
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => {
          if (prev >= 85) return prev;
          return Math.min(prev + Math.random() * 12, 85);
        });
      }, 400);

      // Build specialized prompt for the agent
      const agentPrompt = `Create a ${agent.category} website using the ${agent.name} style.

User Request: "${prompt}"

Agent Specifications:
- Style: ${agent.short_description}
- Colors: Primary: ${agent.style_tokens?.colors?.primary}, Background: ${agent.style_tokens?.colors?.background}, Accent: ${agent.style_tokens?.colors?.accent}
- Typography: Heading: ${agent.style_tokens?.fonts?.heading}, Body: ${agent.style_tokens?.fonts?.base}
- Components available: ${agent.components?.map(c => c.type).join(', ')}

Generate a detailed website concept that follows this agent's design principles and includes:
- Website structure and layout
- Content sections and features
- Design elements that match the agent's style
- Technical implementation approach`;

      setGenerationProgress(30);

      // Call LLM with agent-specific context
      // Use backend-powered generator for web app code
      const webFileTree = await generateWebAppCode(agentPrompt);
      localStorage.setItem("latest_web_file_tree", JSON.stringify(webFileTree));

      // Mock response to keep downstream flow intact
      const response = { website_name: prompt || "Web Project", description: prompt, pages: [], design_elements: [] }; // eslint-disable-line
      /*
        prompt: agentPrompt,
        add_context_from_internet: false,
        response_json_schema: {
          type: "object",
          properties: {
            website_name: { type: "string" },
            description: { type: "string" },
            pages: { 
              type: "array", 
              items: {
                type: "object",
                properties: {
                  name: { type: "string" },
                  sections: { type: "array", items: { type: "string" } }
                }
              }
            },
            design_elements: {
              type: "object",
              properties: {
                color_scheme: { type: "string" },
                typography: { type: "string" },
                layout_style: { type: "string" }
              }
            },
            key_features: { type: "array", items: { type: "string" } },
            technical_stack: { type: "array", items: { type: "string" } }
          },
          required: ["website_name", "description", "pages", "key_features"]
        }
      });
      */

      clearInterval(progressInterval);
      setGenerationProgress(90);

      // Validate response
      if (!response?.website_name || !response?.description) {
        throw new Error('Invalid AI response. Please try again with a more detailed prompt.');
      }

      // Create project record
      const projectData = {
        project_id: "proj_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9),
        agent_id: agent.agent_id,
        prompt: prompt.trim(),
        ast: {
          agent_config: agent,
          generated_content: response,
          pages: response.pages || [],
          design_system: {
            colors: agent.style_tokens?.colors || {},
            fonts: agent.style_tokens?.fonts || {},
            spacing: agent.style_tokens?.spacing || {}
          }
        },
        preview_url: `https://preview.noryon.ai/project/${Date.now()}`,
        download_zip: `https://cdn.noryon.ai/exports/${Date.now()}.zip`,
        status: "completed",
        generation_metadata: {
          generation_time_ms: Date.now(),
          llm_model: "gpt-4",
          agent_version: agent.version || 1
        }
      };

      setGenerationProgress(100);

      // Save project (attempt to save, but don't block on failure)
      try {
        await Project.create(projectData);
      } catch (saveError) {
        console.warn("Failed to save project:", saveError);
        // Continue anyway - user can still see results
      }
      
      setGeneratedProject(projectData);
      
    } catch (error) {
      console.error("Generation error:", error);
      setError(error.message || 'Failed to generate website. Please try again with a different prompt.');
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  };

  const handleCopyPrompt = (examplePrompt) => {
    setPrompt(examplePrompt);
  };

  const handleViewPreview = () => {
    if (generatedProject?.preview_url) {
      window.open(generatedProject.preview_url, '_blank');
    }
  };

  const handleDownloadCode = () => {
    if (generatedProject?.download_zip) {
      window.open(generatedProject.download_zip, '_blank');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#0a0a0a] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-gray-400">Loading agent...</p>
        </div>
      </div>
    );
  }

  if (!agent) {
    return (
      <div className="min-h-screen bg-[#0a0a0a] flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-gray-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-white mb-2">Agent not found</h2>
          <p className="text-gray-400 mb-6">The requested agent could not be loaded.</p>
          {error && (
            <p className="text-red-400 mb-4 text-sm">{error}</p>
          )}
          <div className="flex gap-3 justify-center">
            <Button 
              variant="outline" 
              onClick={() => navigate(createPageUrl("AgentGallery"))}
              className="border-gray-700 text-gray-300 hover:bg-gray-800"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Agents
            </Button>
            <Button onClick={() => window.location.reload()}>
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0a0a0a] text-white">
      <div className="max-w-6xl mx-auto px-6 py-8">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center gap-4 mb-6">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate(createPageUrl("AgentGallery"))}
              className="hover:bg-gray-800 text-gray-400 hover:text-white"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-white">Create with {agent.name}</h1>
              <p className="text-gray-400">{agent.short_description}</p>
            </div>
          </div>
          
          {error && (
            <div className="p-4 bg-red-900/20 border border-red-500/50 rounded-lg mb-6">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-red-400">{error}</p>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => setError("")}
                    className="text-red-400 hover:text-red-300 mt-2 h-auto p-0"
                  >
                    Dismiss
                  </Button>
                </div>
              </div>
            </div>
          )}
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-8">
          
          {/* Left: Agent Info & Prompt */}
          <div className="space-y-6">
            
            {/* Agent Preview */}
            <Card className="bg-[#1a1a1a] border-gray-800">
              <CardContent className="p-6">
                <div className="flex gap-4 mb-4">
                  <img 
                    src={agent.thumbnail_url} 
                    alt={agent.name}
                    className="w-20 h-20 rounded-lg object-cover"
                    onError={(e) => {
                      e.target.src = "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=80&h=80&fit=crop";
                    }}
                  />
                  <div className="flex-1">
                    <h3 className="font-bold text-white mb-1">{agent.name}</h3>
                    <p className="text-gray-400 text-sm mb-2">{agent.short_description}</p>
                    <div className="flex flex-wrap gap-1">
                      {agent.tags?.map((tag, i) => (
                        <Badge key={i} variant="secondary" className="text-xs bg-gray-800 text-gray-300 border-gray-700">
                          {tag}
                        </Badge>
                      )) || null}
                    </div>
                  </div>
                </div>

                {/* Style Preview */}
                {agent.style_tokens && (
                  <div className="space-y-3">
                    <div>
                      <span className="text-sm font-medium text-gray-300">Color Palette:</span>
                      <div className="flex gap-2 mt-1">
                        {agent.style_tokens.colors && (
                          <>
                            <div 
                              className="w-8 h-8 rounded border border-gray-600"
                              style={{ backgroundColor: agent.style_tokens.colors.primary }}
                              title="Primary"
                            />
                            <div 
                              className="w-8 h-8 rounded border border-gray-600"
                              style={{ backgroundColor: agent.style_tokens.colors.background }}
                              title="Background"
                            />
                            <div 
                              className="w-8 h-8 rounded border border-gray-600"
                              style={{ backgroundColor: agent.style_tokens.colors.accent }}
                              title="Accent"
                            />
                          </>
                        )}
                      </div>
                    </div>
                    
                    {agent.style_tokens.fonts && (
                      <div>
                        <span className="text-sm font-medium text-gray-300">Typography:</span>
                        <div className="mt-1 text-sm text-gray-400">
                          <div>Heading: {agent.style_tokens.fonts.heading}</div>
                          <div>Body: {agent.style_tokens.fonts.base}</div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Prompt Input */}
            <Card className="bg-[#1a1a1a] border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Sparkles className="w-5 h-5 text-blue-400" />
                  Describe Your Website
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Textarea
                  placeholder={agent.example_prompts?.[0] ? `Example: "${agent.example_prompts[0]}"` : "Describe the website you want to create..."}
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="min-h-[120px] mb-4 bg-gray-900 border-gray-700 text-white placeholder-gray-500 resize-none"
                  disabled={isGenerating}
                />
                
                {/* Example Prompts */}
                {agent.example_prompts && agent.example_prompts.length > 0 && (
                  <div className="mb-4">
                    <span className="text-sm font-medium text-gray-300 mb-2 block">Example prompts:</span>
                    <div className="space-y-1">
                      {agent.example_prompts.slice(0, 3).map((example, i) => (
                        <button
                          key={i}
                          onClick={() => handleCopyPrompt(example)}
                          className="flex items-center gap-2 text-left text-sm text-blue-400 hover:text-blue-300 hover:underline w-full group"
                          disabled={isGenerating}
                        >
                          <Copy className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0" />
                          <span>"{example}"</span>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                <Button 
                  onClick={handleGenerate}
                  disabled={!prompt.trim() || isGenerating}
                  className="w-full bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  {isGenerating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Generating... {Math.round(generationProgress)}%
                    </>
                  ) : (
                    <>
                      <Zap className="w-4 h-4 mr-2" />
                      Generate Website
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Right: Generation Status & Result */}
          <div className="space-y-6">
            
            <AnimatePresence mode="wait">
              {!isGenerating && !generatedProject && (
                <motion.div
                  key="waiting"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  <Card className="bg-[#1a1a1a] border-gray-800 h-96 flex items-center justify-center">
                    <div className="text-center">
                      <Code className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-white mb-2">Ready to Generate</h3>
                      <p className="text-gray-400">Enter your prompt and click generate to create your website</p>
                    </div>
                  </Card>
                </motion.div>
              )}

              {isGenerating && (
                <motion.div
                  key="generating"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  <Card className="bg-[#1a1a1a] border-gray-800 h-96 flex items-center justify-center">
                    <div className="text-center max-w-sm">
                      <div className="relative w-20 h-20 mx-auto mb-6">
                        <div className="absolute inset-0 border-4 border-gray-700 rounded-full"></div>
                        <div 
                          className="absolute inset-0 border-4 border-blue-500 rounded-full transition-all duration-500"
                          style={{
                            background: `conic-gradient(#3b82f6 ${generationProgress * 3.6}deg, transparent ${generationProgress * 3.6}deg)`
                          }}
                        />
                        <div className="absolute inset-2 bg-[#1a1a1a] rounded-full flex items-center justify-center">
                          <span className="text-blue-400 font-bold text-sm">{Math.round(generationProgress)}%</span>
                        </div>
                      </div>
                      <h3 className="text-lg font-semibold text-white mb-2">Generating Your Website</h3>
                      <p className="text-gray-400">
                        {generationProgress < 30 ? 'Analyzing your prompt...' :
                         generationProgress < 60 ? 'Building components...' :
                         generationProgress < 85 ? 'Applying agent styles...' : 'Finalizing details...'}
                      </p>
                      <p className="text-xs text-gray-500 mt-2">
                        Using {agent.name} • This usually takes 10-15 seconds
                      </p>
                    </div>
                  </Card>
                </motion.div>
              )}

              {generatedProject && (
                <motion.div
                  key="result"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  <Card className="bg-[#1a1a1a] border-gray-800">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-green-400">
                        <CheckCircle className="w-5 h-5" />
                        Website Generated Successfully!
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {/* Generated Content Summary */}
                        <div className="bg-gray-900/50 rounded-lg p-4">
                          <h4 className="font-medium text-white mb-2">
                            {generatedProject.ast?.generated_content?.website_name || "Your Website"}
                          </h4>
                          <p className="text-gray-400 text-sm mb-3">
                            {generatedProject.ast?.generated_content?.description || generatedProject.prompt}
                          </p>
                          
                          {/* Key Features */}
                          {generatedProject.ast?.generated_content?.key_features && (
                            <div className="mb-3">
                              <span className="text-xs text-gray-500 font-medium">Key Features:</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {generatedProject.ast.generated_content.key_features.slice(0, 4).map((feature, i) => (
                                  <Badge key={i} variant="secondary" className="text-xs bg-blue-900/30 text-blue-300">
                                    {feature}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-3">
                          <Button 
                            onClick={handleViewPreview}
                            className="flex-1 bg-blue-600 hover:bg-blue-700"
                          >
                            <Globe className="w-4 h-4 mr-2" />
                            Preview Website
                          </Button>
                          <Button 
                            variant="outline"
                            onClick={handleDownloadCode}
                            className="flex-1 border-gray-700 text-gray-300 hover:bg-gray-800"
                          >
                            <Download className="w-4 h-4 mr-2" />
                            Download Code
                          </Button>
                        </div>

                        {/* Additional Info */}
                        <div className="text-center pt-4 border-t border-gray-800">
                          <p className="text-xs text-gray-500 mb-2">
                            Generated using {agent.name} • Project ID: {generatedProject.project_id}
                          </p>
                          <div className="flex gap-2 justify-center">
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => {
                                setGeneratedProject(null);
                                setPrompt("");
                              }}
                              className="text-gray-400 hover:text-white text-xs h-auto py-1"
                            >
                              Generate Another
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => navigate(createPageUrl("Dashboard"))}
                              className="text-gray-400 hover:text-white text-xs h-auto py-1"
                            >
                              View in Dashboard →
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </div>
  );
}